import React from "react";
import aicpa from "../../../public/assets/images/aicpa.png";
import pipeda from "../../../public/assets/images/pipeda.png";
import hipaa from "../../../public/assets/images/hipaa.png";
import phipa from "../../../public/assets/images/phipa.png";

const complianceLogos = [
	{ src: aicpa.src, alt: "AICPA SOC" },
	{ src: pipeda.src, alt: "PIPEDA Flag" },
	{ src: hipaa.src, alt: "HIPAA Compliant" },
	{ src: phipa.src, alt: "PHIPA Compliant" },
];

export const Complaints = () => {
	return (
		<section className="mx-auto mb-[50px] flex w-full max-w-[1216px] items-center justify-center py-[52px] font-inter lg:px-10 lg:py-[96px]">
			<div className="flex w-full flex-col items-center justify-between lg:flex-row">
				<div className="flex flex-col text-center md:max-w-[478px] lg:text-left msm:max-w-[311px]">
					<span className="mb-2 font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
						Security and Compliance
					</span>
					<h2 className="mb-2 text-xl font-bold text-[#0C0D0E] lg:text-[32px]">
						Secure Compliant Trusted
					</h2>
					<p className="text-sm text-[#68778D] lg:text-lg">
						Built with Enterprise grade security and full compliance
						to meet the highest standards of data privacy and
						protection.
					</p>
				</div>

				<div className="mt-12 flex max-w-[312px] flex-wrap items-center justify-center gap-12 px-12 md:max-w-md lg:mt-0 lg:max-w-none lg:justify-start">
					{complianceLogos.map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-14 w-auto"
						/>
					))}
				</div>
			</div>
		</section>
	);
};
