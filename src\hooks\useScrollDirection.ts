import { useState, useEffect, useRef, useCallback } from "react";

interface UseScrollDirectionReturn {
	scrollDirection: "up" | "down" | null;
	isVisible: boolean;
}

// Custom hook for media query detection
function useMediaQuery(query: string): boolean {
	const [matches, setMatches] = useState(false);

	useEffect(() => {
		const media = window.matchMedia(query);
		if (media.matches !== matches) {
			setMatches(media.matches);
		}
		const listener = () => setMatches(media.matches);
		media.addEventListener("change", listener);
		return () => media.removeEventListener("change", listener);
	}, [matches, query]);

	return matches;
}

export const useScrollDirection = (
	threshold: number = 10,
	enableResponsive: boolean = false
): UseScrollDirectionReturn => {
	const [scrollDirection, setScrollDirection] = useState<
		"up" | "down" | null
	>(null);
	const [isVisible, setIsVisible] = useState(true);
	const lastScrollY = useRef(0);
	const ticking = useRef(false);

	// Check if screen is desktop size (md breakpoint: 600px)
	const isDesktop = useMediaQuery("(min-width: 600px)");

	const updateScrollDirection = useCallback(() => {
		const scrollY = window.scrollY;

		// If responsive mode is enabled and we're on desktop, always keep navbar visible
		if (enableResponsive && isDesktop) {
			setIsVisible(true);
			setScrollDirection(null);
			lastScrollY.current = scrollY;
			ticking.current = false;
			return;
		}

		// Original scroll logic for mobile or when responsive mode is disabled
		if (scrollY < threshold) {
			setIsVisible(true);
			setScrollDirection(null);
			lastScrollY.current = scrollY;
			ticking.current = false;
			return;
		}

		const direction = scrollY > lastScrollY.current ? "down" : "up";
		const scrollDelta = Math.abs(scrollY - lastScrollY.current);
		if (direction !== scrollDirection && scrollDelta > threshold) {
			setScrollDirection(direction);
			setIsVisible(direction === "up");
		}

		lastScrollY.current = scrollY;
		ticking.current = false;
	}, [enableResponsive, isDesktop, threshold, scrollDirection]);

	const onScroll = useCallback(() => {
		if (!ticking.current) {
			requestAnimationFrame(updateScrollDirection);
			ticking.current = true;
		}
	}, [updateScrollDirection]);

	useEffect(() => {
		lastScrollY.current = window.scrollY;
		window.addEventListener("scroll", onScroll, { passive: true });
		return () => {
			window.removeEventListener("scroll", onScroll);
		};
	}, [onScroll]);
	return { scrollDirection, isVisible };
};
