import React, { useEffect, useRef } from "react";

export const Partners = () => {
	const partnerLogos = [
		{ src: "/assets/images/OH-logo.svg", alt: "Ontario Health" },
		{ src: "/assets/images/UOW.svg", alt: "University of Waterloo" },
		{ src: "/assets/images/NM.svg", alt: "Network Medicals" },
		{ src: "/assets/images/NWT.svg", alt: "North Western Toronto" },
		{ src: "/assets/images/microsoft.svg", alt: "Microsoft" },
		{ src: "/assets/images/velocity.png", alt: "Velocity" },
	];

	const containerRef = useRef(null);
	const isDragging = useRef(false);
	const startX = useRef(0);
	const scrollLeft = useRef(0);
	const isAutoScrolling = useRef(true);

	// Auto-scroll loop
	useEffect(() => {
		let frameId;
		const speed = 0.5;

		const scroll = () => {
			if (isAutoScrolling.current && containerRef.current) {
				containerRef.current.scrollLeft += speed;
				if (
					containerRef.current.scrollLeft >=
					containerRef.current.scrollWidth / 2
				) {
					containerRef.current.scrollLeft = 0;
				}
			}
			frameId = requestAnimationFrame(scroll);
		};

		scroll();
		return () => cancelAnimationFrame(frameId);
	}, []);

	// Pointer/Touch handlers
	const handleDown = (e) => {
		isDragging.current = true;
		isAutoScrolling.current = false;
		startX.current = e.pageX || e.touches?.[0].pageX;
		scrollLeft.current = containerRef.current.scrollLeft;
	};

	const handleMove = (e) => {
		if (!isDragging.current) return;
		const x = e.pageX || e.touches?.[0].pageX;
		const walk = (x - startX.current) * 1.5;
		containerRef.current.scrollLeft = scrollLeft.current - walk;
	};

	const handleUp = () => {
		isDragging.current = false;
		isAutoScrolling.current = true;
	};

	return (
		<section className="w-full py-[52px] sm:mt-[30px] lg:py-12 xl:mt-10">
			{/* Desktop View */}
			<div className="hidden lg:flex lg:items-center lg:justify-center">
				<div className="flex flex-wrap items-center justify-center gap-[52.96px]">
					{partnerLogos.map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-8 w-auto flex-shrink-0 grayscale transition-all duration-300 hover:grayscale-0"
							style={{ maxWidth: 140 }}
						/>
					))}
				</div>
			</div>

			{/* Mobile View */}
			<div
				className="overflow-x-scroll lg:hidden [&::-webkit-scrollbar]:hidden"
				ref={containerRef}
				style={{ cursor: "grab", WebkitOverflowScrolling: "touch" }}
				onMouseDown={handleDown}
				onMouseMove={handleMove}
				onMouseUp={handleUp}
				onMouseLeave={handleUp}
				onTouchStart={handleDown}
				onTouchMove={handleMove}
				onTouchEnd={handleUp}
			>
				<div className="flex w-max gap-[52.96px] py-2">
					{[...partnerLogos, ...partnerLogos].map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-8 w-auto grayscale"
							style={{ maxWidth: 140 }}
						/>
					))}
				</div>
			</div>
		</section>
	);
};
