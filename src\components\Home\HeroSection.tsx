import React, { Dispatch, SetStateAction } from "react";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";

const HeroSection: React.FC<{
	setShowBookDemo: Dispatch<SetStateAction<boolean>>;
}> = ({ setShowBookDemo }) => {
	return (
		<>
			<section id="hero">
				<div className="relative mb-[52px] font-inter sm:mb-[66px] md:mb-[82px]">
					<div className="h-[500px] w-full bg-[url('/assets/images/hero-bg.svg')] bg-cover bg-bottom bg-no-repeat md:h-[550px] lg:h-[810px] xl:h-[779px]">
						<div className="relative z-10 mx-auto flex w-full max-w-[288px] flex-col items-center justify-center pt-32 text-center sm:max-w-[312px] md:max-w-[552px] lg:max-w-[900px]">
							<h1 className="text-2xl font-bold leading-tight text-white lg:text-[38px]">
								AI-Powered Solution for{" "}
								<span className="text-[#72F4E8]">
									Healthcare
								</span>{" "}
								Operations
							</h1>
							<p className="mx-auto mt-4 max-w-[288px] text-base text-white/70 sm:max-w-[880px] sm:text-sm md:text-lg">
								The all-in-one platform that integrates
								seamlessly with EMRs and existing tools to
								streamline workflows, reduce admin tasks, boost
								efficiency, and enhance patient care.
							</p>
							<div className="mt-4 hidden flex-row items-center justify-center gap-4 lg:flex xl:mt-[33px]">
								<Button
									aria-label="Book a Demo"
									onClick={() => setShowBookDemo(true)}
									disabled={false}
									className="rounded-md bg-white px-4 py-2.5 font-sans text-sm font-medium text-[#032F57] shadow-none transition hover:bg-[#72F4E8] hover:text-[#032F57]"
								>
									Get Started
								</Button>
								<Link href="#features">
									<Button
										variant="outline"
										className="rounded-md border border-white bg-transparent px-4 py-2.5 font-sans text-sm font-medium text-white transition hover:border-[#72F4E8] hover:bg-transparent hover:text-[#72F4E8]"
									>
										Explore Features
									</Button>
								</Link>
							</div>
						</div>
					</div>
					<div className="flex items-center justify-center">
						<div className="absolute -bottom-8 h-[162.06px] w-full max-w-[288px] rounded-2xl border border-white bg-[#032F57] shadow-[0px_4px_50px_0px_#00000040] sm:-bottom-10 sm:h-[207px] sm:max-w-[312px] md:-bottom-[60px] md:h-[310px] md:max-w-[552px] lg:h-[486.19px] lg:max-w-[864px] xl:-bottom-[81px] xl:h-[480px] xl:max-w-[853px]">
							<iframe
								className="h-full w-full rounded-2xl"
								src="https://www.youtube.com/embed/PPxZGdZg8L8?autoplay=1&mute=1&loop=1&playlist=PPxZGdZg8L8"
								title="Migranium Healthcare Platform"
								frameBorder="0"
								allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
								allowFullScreen
							></iframe>
						</div>
					</div>{" "}
				</div>
				<div className="flex items-center justify-center">
					<Button
						className="my-7 h-[52px] w-full max-w-[288px] whitespace-nowrap bg-[#053969] text-white ease-in-out hover:text-[#72F4E8] sm:max-w-[312px] md:max-w-[142px] lg:hidden"
						disabled={false}
					>
						Get Started{" "}
					</Button>{" "}
				</div>
			</section>
			<p className="fixed left-[2000px] hidden cursor-default select-none text-transparent">
				Title: Enhance Operational Efficiency with Migranium: Premier AI
				Solution for Healthcare and Business Sectors Meta
				Description:Maximize efficiency with Migranium&apos;s all-in-one
				AI-driven scheduling, queue management, and process automation
				software. Tailored for healthcare providers, clinics, hospitals,
				and businesses, Migranium streamlines operations with smart AI
				solutions, EMR automation, customizable workflows, and real-time
				analytics. H1: Migranium - Redefining Operational Excellence
				with AI for Healthcare and Business Operations Introduction: In
				the competitive worlds of healthcare and business, operational
				efficiency isn&apos;t just a goal—it&apos;s a necessity.
				Migranium, powered by advanced AI, leads this transformation,
				offering a suite of intelligent tools that streamline processes,
				optimize patient care, and unlock actionable insights. Whether
				you&apos;re managing a clinic, hospital, or growing business,
				Migranium&apos;s all-in-one AI platform offers comprehensive
				solutions for optimizing AI-driven patient scheduling, workflow
				automation, queue management, and performance analysis. With
				Migranium, you can eliminate inefficiencies, improve patient
				satisfaction, and scale your operations seamlessly. Embrace the
				future of operational excellence with AI-powered solutions
				today. H2: Unparalleled AI-Driven Operational Insights for
				Strategic Growth Gain a competitive edge with Migranium&apos;s
				AI-powered Operational Insights. Our advanced analytics dive
				deep into your data, providing clarity on key metrics such as
				provider availability, patient demand, and appointment
				scheduling. Migranium&apos;s real-time AI data insights help you
				identify inefficiencies, optimize workflows, and uncover
				opportunities for growth. By leveraging data-driven AI
				decision-making, healthcare providers and businesses alike can
				make informed decisions that drive strategic planning, improve
				patient outcomes, and enhance resource management. With
				AI-enabled customizable dashboards and intuitive reporting,
				Migranium ensures that your operations are always aligned with
				your objectives, leading to optimized performance and measurable
				outcomes. H2: Adaptive AI Process Configuration for Dynamic
				Environments Migranium&apos;s AI Process Configuration adapts to
				the changing needs of your operations. Whether you&apos;re
				managing a bustling clinic, a hospital with varying patient
				demands, or a growing business, Migranium offers AI-powered
				customizable workflows that evolve with your needs. Our
				all-in-one staff planner and AI workflow automation tools ensure
				that your processes are as flexible as your environment,
				accommodating changes in patient scheduling, staff availability,
				and resource management. With Migranium&apos;s AI automation,
				routine tasks can be automated, freeing up your team to focus on
				high-value work while maintaining continuous patient care.
				AI-configurable templates allow you to tailor workflows to meet
				specific EMR integration and patient care requirements, making
				your operations more efficient, responsive, and scalable. H2:
				Real-Time Remote AI Monitoring for Operational Control Stay
				connected to every aspect of your operations with
				Migranium&apos;s AI-powered Remote Monitoring. Oversee
				workflows, patient queues, and staff performance from anywhere,
				ensuring that your operations are efficient and seamless.
				Migranium&apos;s real-time AI monitoring capabilities allow you
				to track queue management, patient flow, and other critical
				activities in real time. With this AI-driven functionality, you
				can ensure smooth operations, identify bottlenecks immediately,
				and make proactive adjustments. Migranium&apos;s AI remote
				monitoring also supports EMR automation, ensuring healthcare
				staff have real-time access to up-to-date patient information,
				wherever they are. H2: Streamlined Customer & Patient Management
				for Peak Satisfaction Elevate the patient experience with
				Migranium&apos;s AI Customer Management system. By optimizing
				patient intake, AI-driven appointment scheduling, and customer
				flow, Migranium helps you deliver service that&apos;s not just
				efficient but exceptional. Migranium&apos;s AI queue management
				feature ensures that your patients are never left waiting,
				providing a seamless experience that drives patient satisfaction
				and loyalty. With automated appointment reminders, combined with
				intelligent scheduling, Migranium makes it easy to manage
				appointments, reduce no-shows, and improve patient retention.
				Happy patients lead to a thriving healthcare business, and with
				Migranium&apos;s intelligent AI automation, patient care
				satisfaction becomes the cornerstone of your success. Patient
				insights and AI-driven feedback loops continuously improve
				service offerings, strengthening relationships and driving
				repeat business. H3: Choose Migranium for: AI-Powered Decision
				Making: Empower your strategy with AI analytics, making informed
				choices based on real-time data such as patient demand, provider
				availability, and operational efficiency. Customizable AI
				Workflows: Tailor your processes and workflows to fit your
				unique operational needs, from EMR integration to patient
				scheduling, all powered by AI. Real-Time AI Monitoring: Stay
				ahead with 24/7 oversight of your business operations, including
				AI-powered queue management, patient flow, and staff scheduling.
				Enhanced Patient Experience: Deliver exceptional service with
				AI-driven patient intake, intelligent queue management, and
				personalized scheduling for every patient. Conclusion:Migranium
				isn&apos;t just a tool—it&apos;s an AI-powered strategic ally in
				your quest for operational mastery. Our commitment to AI-driven
				innovation, efficiency, and patient satisfaction makes us the
				ideal partner for any clinic, hospital, or business ready to
				scale new heights. From AI scheduling automation to staff
				planner solutions, EMR integration, and workflow optimization,
				Migranium provides a comprehensive suite of features designed to
				meet the unique needs of the healthcare and business sectors.
				Step into the future with Migranium, where AI-powered operations
				and customer service excellence are the standard. Call to
				Action: Ready to revolutionize your operations? Contact us now
				to schedule a demo and see how Migranium&apos;s AI-powered
				automation, custom workflows, and real-time operational insights
				can elevate your efficiency and patient satisfactionto the next
				level. Don&apos;t wait—unlock your organization&apos;s full
				potential today with Migranium.
			</p>
		</>
	);
};

export default HeroSection;
