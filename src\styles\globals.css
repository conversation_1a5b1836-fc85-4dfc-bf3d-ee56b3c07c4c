/* @import url("https://fonts.cdnfonts.com/css/tt-hoves-pro-trial"); */
/* @import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap"); */
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Shantell+Sans:ital,wght@0,300..800;1,300..800&display=swap");
@import url("hamburger.module.css");
@import url("loader.module.css");
@import url("signup.module.css");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;
		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;
		/* --primary: 204 100% 29%; */
		--primary: 0 89 148;
		--primary-foreground: 24 48% 90%;
		--secondary: 174 86% 80%;
		--secondary-foreground: 222.2 47.4% 11.2%;
		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;
		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;
		--destructive: 2 64% 48%;
		--destructive-foreground: 0 0% 20%;
		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 222.2 84% 4.9%;
		--radius: 0.5rem;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
	}

	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;
		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;
		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;
		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;
		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;
		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;
		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;
		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
	}

	html {
		/* font-family: "DM Sans", serif; */
		font-synthesis: none;
		text-rendering: optimizeLegibility;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		-webkit-text-size-adjust: 100%;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}
}

.custom-accordion-item {
	overflow: hidden;
}

.custom-accordion-item button {
	position: relative;
	transition: all 0.3s ease;
}

.custom-accordion-item button:hover {
	color: #0d9488;
}

.custom-accordion-item button:focus {
	outline: none;
}

.custom-accordion-item [aria-expanded="true"] + div {
	animation: slideDown 0.3s ease-out forwards;
}

.custom-accordion-item [aria-expanded="false"] + div {
	animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideDown {
	from {
		max-height: 0;
		opacity: 0;
	}

	to {
		max-height: 500px;
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		max-height: 500px;
		opacity: 1;
	}

	to {
		max-height: 0;
		opacity: 0;
	}
}

.navbar-scroll-responsive {
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-hidden {
	transform: translateY(-100%);
}

.navbar-visible {
	transform: translateY(0);
}

html {
	scroll-behavior: smooth;
}
